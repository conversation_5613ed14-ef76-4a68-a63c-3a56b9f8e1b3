import React, { useState, useEffect } from 'react'
import { Routes, Route } from 'react-router-dom'
import { motion } from 'framer-motion'
import Header from './components/Header'
import Sidebar from './components/Sidebar'
import FoodGrid from './components/FoodGrid'
import Cart from './components/Cart'
import FloatingWhatsApp from './components/FloatingWhatsApp'
import ThemeToggle from './components/ThemeToggle'
import { useCartStore } from './store/cartStore'
import { useThemeStore } from './store/themeStore'

function App() {
  const { isCartOpen } = useCartStore()
  const { isDark } = useThemeStore()
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [searchQuery, setSearchQuery] = useState('')

  useEffect(() => {
    if (isDark) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }, [isDark])

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDark ? 'dark bg-gray-900' : 'bg-gray-50'
    }`}>
      <Routes>
        <Route path="/" element={
          <div className="flex flex-col h-screen">
            {/* Header */}
            <Header searchQuery={searchQuery} setSearchQuery={setSearchQuery} />
            
            {/* Main Content */}
            <div className="flex flex-1 overflow-hidden">
              {/* Sidebar */}
              <Sidebar 
                selectedCategory={selectedCategory}
                setSelectedCategory={setSelectedCategory}
              />
              
              {/* Food Grid */}
              <main className="flex-1 overflow-y-auto p-4 lg:p-6">
                <FoodGrid 
                  selectedCategory={selectedCategory}
                  searchQuery={searchQuery}
                />
              </main>
            </div>
            
            {/* Cart Drawer */}
            <Cart />
            
            {/* Floating Elements */}
            <FloatingWhatsApp />
            <ThemeToggle />
          </div>
        } />
        
        {/* Additional routes will be added here */}
        <Route path="/orders" element={<div>Orders Page</div>} />
        <Route path="/profile" element={<div>Profile Page</div>} />
        <Route path="/admin" element={<div>Admin Dashboard</div>} />
      </Routes>
    </div>
  )
}

export default App
