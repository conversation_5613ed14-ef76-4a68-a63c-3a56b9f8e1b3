import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export const useCartStore = create(
  persist(
    (set, get) => ({
      items: [],
      isCartOpen: false,
      totalItems: 0,
      totalPrice: 0,

      // Add item to cart
      addItem: (product, size = '250ml', quantity = 1) => {
        const { items } = get()
        const existingItemIndex = items.findIndex(
          item => item.id === product.id && item.size === size
        )

        if (existingItemIndex > -1) {
          // Update existing item
          const updatedItems = [...items]
          updatedItems[existingItemIndex].quantity += quantity
          set({ items: updatedItems })
        } else {
          // Add new item
          const newItem = {
            ...product,
            size,
            quantity,
            cartId: `${product.id}-${size}-${Date.now()}`
          }
          set({ items: [...items, newItem] })
        }
        
        get().calculateTotals()
      },

      // Remove item from cart
      removeItem: (cartId) => {
        const { items } = get()
        const updatedItems = items.filter(item => item.cartId !== cartId)
        set({ items: updatedItems })
        get().calculateTotals()
      },

      // Update item quantity
      updateQuantity: (cartId, quantity) => {
        if (quantity <= 0) {
          get().removeItem(cartId)
          return
        }

        const { items } = get()
        const updatedItems = items.map(item =>
          item.cartId === cartId ? { ...item, quantity } : item
        )
        set({ items: updatedItems })
        get().calculateTotals()
      },

      // Clear cart
      clearCart: () => {
        set({ items: [], totalItems: 0, totalPrice: 0 })
      },

      // Toggle cart drawer
      toggleCart: () => {
        set(state => ({ isCartOpen: !state.isCartOpen }))
      },

      // Close cart
      closeCart: () => {
        set({ isCartOpen: false })
      },

      // Open cart
      openCart: () => {
        set({ isCartOpen: true })
      },

      // Calculate totals
      calculateTotals: () => {
        const { items } = get()
        const totalItems = items.reduce((sum, item) => sum + item.quantity, 0)
        const totalPrice = items.reduce((sum, item) => {
          const price = item.sizes?.[item.size] || item.price
          return sum + (price * item.quantity)
        }, 0)
        
        set({ totalItems, totalPrice })
      },

      // Get cart summary
      getCartSummary: () => {
        const { items, totalItems, totalPrice } = get()
        return {
          items,
          totalItems,
          totalPrice,
          isEmpty: items.length === 0
        }
      }
    }),
    {
      name: 'cart-storage',
      partialize: (state) => ({
        items: state.items,
        totalItems: state.totalItems,
        totalPrice: state.totalPrice
      })
    }
  )
)
