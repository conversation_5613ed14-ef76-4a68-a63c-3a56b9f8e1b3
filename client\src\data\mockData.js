export const mockDishes = [
  // Maggi Items
  {
    id: 'maggi-1',
    name: 'Classic Masala Maggi',
    description: 'Traditional 2-minute noodles with aromatic masala spices',
    price: 45,
    originalPrice: 50,
    image: 'https://images.unsplash.com/photo-1585032226651-759b368d7246?w=400&h=300&fit=crop',
    category: 'maggi',
    dietary: 'veg',
    rating: 4.5,
    prepTime: 5,
    orders: 1250,
    tags: ['bestseller', 'quick'],
    sizes: {
      '250ml': 45,
      '450ml': 75
    }
  },
  {
    id: 'maggi-2',
    name: 'Cheese Burst Maggi',
    description: 'Creamy cheese-loaded maggi with extra vegetables',
    price: 65,
    image: 'https://images.unsplash.com/photo-1612929633738-8fe44f7ec841?w=400&h=300&fit=crop',
    category: 'maggi',
    dietary: 'veg',
    rating: 4.7,
    prepTime: 7,
    orders: 890,
    tags: ['bestseller'],
    sizes: {
      '250ml': 65,
      '450ml': 95
    }
  },
  {
    id: 'maggi-3',
    name: 'Egg Maggi Special',
    description: 'Protein-rich maggi with scrambled eggs and spices',
    price: 55,
    image: 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=400&h=300&fit=crop',
    category: 'maggi',
    dietary: 'egg',
    rating: 4.6,
    prepTime: 8,
    orders: 670,
    tags: ['new'],
    sizes: {
      '250ml': 55,
      '450ml': 85
    }
  },

  // Fried Rice Items
  {
    id: 'rice-1',
    name: 'Veg Fried Rice',
    description: 'Aromatic basmati rice stir-fried with fresh vegetables',
    price: 120,
    image: 'https://images.unsplash.com/photo-1603133872878-684f208fb84b?w=400&h=300&fit=crop',
    category: 'fried-rice',
    dietary: 'veg',
    rating: 4.4,
    prepTime: 15,
    orders: 980,
    tags: ['offer'],
    sizes: {
      'Half': 120,
      'Full': 180
    }
  },
  {
    id: 'rice-2',
    name: 'Chicken Fried Rice',
    description: 'Tender chicken pieces with perfectly seasoned rice',
    price: 160,
    image: 'https://images.unsplash.com/photo-1512058564366-18510be2db19?w=400&h=300&fit=crop',
    category: 'fried-rice',
    dietary: 'non-veg',
    rating: 4.8,
    prepTime: 18,
    orders: 1100,
    tags: ['bestseller'],
    sizes: {
      'Half': 160,
      'Full': 240
    }
  },

  // Biryani Items
  {
    id: 'biryani-1',
    name: 'Hyderabadi Chicken Biryani',
    description: 'Authentic Hyderabadi-style biryani with tender chicken',
    price: 280,
    image: 'https://images.unsplash.com/photo-1563379091339-03246963d96c?w=400&h=300&fit=crop',
    category: 'biryani',
    dietary: 'non-veg',
    rating: 4.9,
    prepTime: 25,
    orders: 750,
    tags: ['bestseller'],
    sizes: {
      'Half': 280,
      'Full': 420
    }
  },
  {
    id: 'biryani-2',
    name: 'Veg Dum Biryani',
    description: 'Fragrant basmati rice with mixed vegetables and spices',
    price: 220,
    image: 'https://images.unsplash.com/photo-1596797038530-2c107229654b?w=400&h=300&fit=crop',
    category: 'biryani',
    dietary: 'veg',
    rating: 4.5,
    prepTime: 22,
    orders: 560,
    tags: ['offer'],
    sizes: {
      'Half': 220,
      'Full': 320
    }
  },

  // Noodles Items
  {
    id: 'noodles-1',
    name: 'Hakka Noodles',
    description: 'Indo-Chinese style noodles with vegetables',
    price: 110,
    image: 'https://images.unsplash.com/photo-1555126634-323283e090fa?w=400&h=300&fit=crop',
    category: 'noodles',
    dietary: 'veg',
    rating: 4.3,
    prepTime: 12,
    orders: 820,
    tags: ['quick'],
    sizes: {
      'Half': 110,
      'Full': 170
    }
  },
  {
    id: 'noodles-2',
    name: 'Schezwan Noodles',
    description: 'Spicy Schezwan sauce noodles with vegetables',
    price: 130,
    image: 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=400&h=300&fit=crop',
    category: 'noodles',
    dietary: 'veg',
    rating: 4.6,
    prepTime: 14,
    orders: 690,
    tags: ['bestseller'],
    sizes: {
      'Half': 130,
      'Full': 190
    }
  },

  // Sandwiches
  {
    id: 'sandwich-1',
    name: 'Grilled Cheese Sandwich',
    description: 'Crispy grilled sandwich with melted cheese',
    price: 80,
    image: 'https://images.unsplash.com/photo-1528735602780-2552fd46c7af?w=400&h=300&fit=crop',
    category: 'sandwiches',
    dietary: 'veg',
    rating: 4.4,
    prepTime: 8,
    orders: 450,
    tags: ['quick'],
    sizes: {
      'Single': 80,
      'Double': 120
    }
  },

  // Beverages
  {
    id: 'beverage-1',
    name: 'Fresh Lime Soda',
    description: 'Refreshing lime soda with mint and ice',
    price: 35,
    image: 'https://images.unsplash.com/photo-1546173159-315724a31696?w=400&h=300&fit=crop',
    category: 'beverages',
    dietary: 'veg',
    rating: 4.2,
    prepTime: 3,
    orders: 920,
    tags: ['quick'],
    sizes: {
      '250ml': 35,
      '450ml': 55
    }
  },
  {
    id: 'beverage-2',
    name: 'Masala Chai',
    description: 'Traditional Indian tea with aromatic spices',
    price: 25,
    image: 'https://images.unsplash.com/photo-1571934811356-5cc061b6821f?w=400&h=300&fit=crop',
    category: 'beverages',
    dietary: 'veg',
    rating: 4.7,
    prepTime: 5,
    orders: 1500,
    tags: ['bestseller'],
    sizes: {
      '150ml': 25,
      '250ml': 40
    }
  },

  // Desserts
  {
    id: 'dessert-1',
    name: 'Chocolate Brownie',
    description: 'Rich and fudgy chocolate brownie with vanilla ice cream',
    price: 95,
    image: 'https://images.unsplash.com/photo-1606313564200-e75d5e30476c?w=400&h=300&fit=crop',
    category: 'desserts',
    dietary: 'veg',
    rating: 4.8,
    prepTime: 10,
    orders: 380,
    tags: ['new'],
    sizes: {
      'Single': 95,
      'Double': 150
    }
  },

  // Snacks
  {
    id: 'snack-1',
    name: 'Samosa Chaat',
    description: 'Crispy samosas topped with chutneys and yogurt',
    price: 60,
    image: 'https://images.unsplash.com/photo-1601050690597-df0568f70950?w=400&h=300&fit=crop',
    category: 'snacks',
    dietary: 'veg',
    rating: 4.5,
    prepTime: 8,
    orders: 720,
    tags: ['bestseller'],
    sizes: {
      '2 Pieces': 60,
      '4 Pieces': 100
    }
  }
]
