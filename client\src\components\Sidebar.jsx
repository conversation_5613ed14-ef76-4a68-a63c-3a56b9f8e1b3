import React from 'react'
import { motion } from 'framer-motion'

const categories = [
  { id: 'all', name: 'All Items', icon: '🍽️', count: 45 },
  { id: 'maggi', name: '<PERSON>il Maggi', icon: '🍜', count: 8 },
  { id: 'fried-rice', name: 'Fried Rice', icon: '🍚', count: 6 },
  { id: 'biryani', name: '<PERSON><PERSON><PERSON><PERSON>', icon: '🍛', count: 5 },
  { id: 'noodles', name: 'Noodles', icon: '🍝', count: 7 },
  { id: 'sandwiches', name: 'Sandwiches', icon: '🥪', count: 4 },
  { id: 'beverages', name: 'Beverages', icon: '🥤', count: 8 },
  { id: 'desserts', name: 'Desser<PERSON>', icon: '🍰', count: 6 },
  { id: 'snacks', name: 'Snacks', icon: '🍿', count: 5 }
]

const Sidebar = ({ selectedCategory, setSelectedCategory }) => {
  return (
    <motion.aside
      initial={{ x: -300 }}
      animate={{ x: 0 }}
      className="w-64 lg:w-72 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 overflow-y-auto"
    >
      <div className="p-4 lg:p-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Categories
        </h2>
        
        <div className="space-y-2">
          {categories.map((category, index) => (
            <motion.button
              key={category.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              whileHover={{ scale: 1.02, x: 4 }}
              whileTap={{ scale: 0.98 }}
              onClick={() => setSelectedCategory(category.id)}
              className={`w-full flex items-center justify-between p-3 rounded-xl transition-all duration-200 ${
                selectedCategory === category.id
                  ? 'bg-primary-500 text-white shadow-lg'
                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
            >
              <div className="flex items-center space-x-3">
                <span className="text-xl">{category.icon}</span>
                <span className="font-medium">{category.name}</span>
              </div>
              <span className={`text-xs px-2 py-1 rounded-full ${
                selectedCategory === category.id
                  ? 'bg-white/20 text-white'
                  : 'bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300'
              }`}>
                {category.count}
              </span>
            </motion.button>
          ))}
        </div>

        {/* Filters Section */}
        <div className="mt-8">
          <h3 className="text-md font-semibold text-gray-900 dark:text-white mb-4">
            Filters
          </h3>
          
          {/* Dietary Preferences */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Dietary Preferences
            </h4>
            <div className="space-y-2">
              {[
                { id: 'veg', label: 'Vegetarian', icon: '🌱' },
                { id: 'egg', label: 'Egg', icon: '🥚' },
                { id: 'non-veg', label: 'Non-Vegetarian', icon: '🍗' }
              ].map((diet) => (
                <label key={diet.id} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    className="rounded border-gray-300 text-primary-500 focus:ring-primary-500"
                  />
                  <span className="text-lg">{diet.icon}</span>
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    {diet.label}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Price Range */}
          <div className="mt-6">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Price Range
            </h4>
            <div className="space-y-2">
              {[
                { label: 'Under ₹50', value: '0-50' },
                { label: '₹50 - ₹100', value: '50-100' },
                { label: '₹100 - ₹200', value: '100-200' },
                { label: 'Above ₹200', value: '200+' }
              ].map((price) => (
                <label key={price.value} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="price"
                    className="border-gray-300 text-primary-500 focus:ring-primary-500"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    {price.label}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Special Tags */}
          <div className="mt-6">
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Special Tags
            </h4>
            <div className="flex flex-wrap gap-2">
              {[
                { label: '🔥 Best Seller', value: 'bestseller' },
                { label: '💸 On Offer', value: 'offer' },
                { label: '⚡ Quick', value: 'quick' },
                { label: '🌟 New', value: 'new' }
              ].map((tag) => (
                <motion.button
                  key={tag.value}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-primary-100 dark:hover:bg-primary-900 transition-colors duration-200"
                >
                  {tag.label}
                </motion.button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </motion.aside>
  )
}

export default Sidebar
