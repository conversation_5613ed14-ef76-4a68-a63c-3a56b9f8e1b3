{"name": "preorder-food-app", "version": "1.0.0", "description": "AI-integrated premium pre-order food application", "private": true, "workspaces": ["client", "server", "shared"], "scripts": {"dev": "concurrently \"npm run dev:client\" \"npm run dev:server\"", "dev:client": "cd client && npm run dev", "dev:server": "cd server && npm run dev", "build": "npm run build:client && npm run build:server", "build:client": "cd client && npm run build", "build:server": "cd server && npm run build", "install:all": "npm install && cd client && npm install && cd ../server && npm install", "clean": "rm -rf node_modules client/node_modules server/node_modules shared/node_modules", "test": "npm run test:client && npm run test:server", "test:client": "cd client && npm test", "test:server": "cd server && npm test"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/preorder-food-app.git"}, "keywords": ["food-delivery", "preorder", "react", "nodejs", "firebase", "openai", "ai-recommendations"], "author": "Your Name", "license": "MIT"}