import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Plus, Minus, Star, Clock, Flame } from 'lucide-react'
import { useCartStore } from '../store/cartStore'
import toast from 'react-hot-toast'

const DishCard = ({ dish }) => {
  const { addItem } = useCartStore()
  const [selectedSize, setSelectedSize] = useState('250ml')
  const [quantity, setQuantity] = useState(1)
  const [isHovered, setIsHovered] = useState(false)

  const currentPrice = dish.sizes?.[selectedSize] || dish.price

  const handleAddToCart = () => {
    addItem(dish, selectedSize, quantity)
    toast.success(`${dish.name} added to cart!`, {
      icon: '🛒',
      duration: 2000,
    })
    setQuantity(1)
  }

  const getDietaryIcon = (type) => {
    switch (type) {
      case 'veg': return '🌱'
      case 'egg': return '🥚'
      case 'non-veg': return '🍗'
      default: return '🌱'
    }
  }

  return (
    <motion.div
      whileHover={{ y: -8, scale: 1.02 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden group"
    >
      {/* Image Container */}
      <div className="relative h-48 overflow-hidden">
        <img
          src={dish.image}
          alt={dish.name}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
        />
        
        {/* Badges */}
        <div className="absolute top-3 left-3 flex flex-col gap-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDietaryIcon(dish.dietary) === '🌱' ? 'bg-green-100 text-green-800' : getDietaryIcon(dish.dietary) === '🥚' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}`}>
            {getDietaryIcon(dish.dietary)} {dish.dietary.charAt(0).toUpperCase() + dish.dietary.slice(1)}
          </span>
          {dish.tags.includes('bestseller') && (
            <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
              <Flame className="w-3 h-3" />
              Best Seller
            </span>
          )}
          {dish.tags.includes('offer') && (
            <span className="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
              💸 On Offer
            </span>
          )}
        </div>

        {/* Rating */}
        <div className="absolute top-3 right-3 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg px-2 py-1 flex items-center gap-1">
          <Star className="w-4 h-4 text-yellow-500 fill-current" />
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            {dish.rating}
          </span>
        </div>

        {/* Quick Add Button (appears on hover) */}
        <motion.button
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: isHovered ? 1 : 0, scale: isHovered ? 1 : 0.8 }}
          onClick={handleAddToCart}
          className="absolute bottom-3 right-3 bg-primary-500 hover:bg-primary-600 text-white p-2 rounded-full shadow-lg transition-colors duration-200"
        >
          <Plus className="w-5 h-5" />
        </motion.button>
      </div>

      {/* Content */}
      <div className="p-4">
        {/* Title and Description */}
        <div className="mb-3">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1 line-clamp-1">
            {dish.name}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
            {dish.description}
          </p>
        </div>

        {/* Prep Time */}
        <div className="flex items-center gap-1 mb-3 text-gray-500 dark:text-gray-400">
          <Clock className="w-4 h-4" />
          <span className="text-sm">{dish.prepTime} mins</span>
        </div>

        {/* Size Selection */}
        {dish.sizes && (
          <div className="mb-3">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
              Size:
            </label>
            <div className="flex gap-2">
              {Object.keys(dish.sizes).map((size) => (
                <button
                  key={size}
                  onClick={() => setSelectedSize(size)}
                  className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors duration-200 ${
                    selectedSize === size
                      ? 'bg-primary-500 text-white'
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                  }`}
                >
                  {size}
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Price and Add to Cart */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-xl font-bold text-primary-500">
              ₹{currentPrice}
            </span>
            {dish.originalPrice && dish.originalPrice > currentPrice && (
              <span className="text-sm text-gray-500 line-through">
                ₹{dish.originalPrice}
              </span>
            )}
          </div>

          {/* Quantity and Add Button */}
          <div className="flex items-center gap-2">
            <div className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg">
              <button
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                className="p-1 text-gray-600 dark:text-gray-300 hover:text-primary-500 transition-colors duration-200"
              >
                <Minus className="w-4 h-4" />
              </button>
              <span className="px-3 py-1 text-sm font-medium text-gray-900 dark:text-white">
                {quantity}
              </span>
              <button
                onClick={() => setQuantity(quantity + 1)}
                className="p-1 text-gray-600 dark:text-gray-300 hover:text-primary-500 transition-colors duration-200"
              >
                <Plus className="w-4 h-4" />
              </button>
            </div>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleAddToCart}
              className="bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Add
            </motion.button>
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default DishCard
