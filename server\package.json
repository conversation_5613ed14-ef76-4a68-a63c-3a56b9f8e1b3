{"name": "preorder-food-server", "version": "1.0.0", "description": "Backend server for AI-integrated pre-order food application", "main": "index.js", "type": "module", "scripts": {"dev": "nodemon index.js", "start": "node index.js", "build": "echo 'No build step required for Node.js'", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "firebase-admin": "^12.0.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "openai": "^4.20.1", "axios": "^1.6.2", "twilio": "^4.19.0", "stripe": "^14.9.0", "razorpay": "^2.9.2", "node-cron": "^3.0.3", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "keywords": ["food-delivery", "api", "nodejs", "express", "firebase", "openai"], "author": "Your Name", "license": "MIT"}