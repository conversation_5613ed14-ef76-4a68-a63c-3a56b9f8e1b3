import React, { useState, useMemo } from 'react'
import { motion } from 'framer-motion'
import DishCard from './DishCard'
import { mockDishes } from '../data/mockData'

const FoodGrid = ({ selectedCategory, searchQuery }) => {
  const [sortBy, setSortBy] = useState('popular')

  const filteredDishes = useMemo(() => {
    let filtered = mockDishes

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(dish => dish.category === selectedCategory)
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(dish =>
        dish.name.toLowerCase().includes(query) ||
        dish.description.toLowerCase().includes(query) ||
        dish.tags.some(tag => tag.toLowerCase().includes(query))
      )
    }

    // Sort dishes
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price)
        break
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price)
        break
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating)
        break
      case 'popular':
      default:
        filtered.sort((a, b) => b.orders - a.orders)
        break
    }

    return filtered
  }, [selectedCategory, searchQuery, sortBy])

  return (
    <div className="space-y-6">
      {/* Header with Sort Options */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">
            {selectedCategory === 'all' ? 'All Dishes' : 
             selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1).replace('-', ' ')}
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {filteredDishes.length} dishes found
            {searchQuery && ` for "${searchQuery}"`}
          </p>
        </div>

        <div className="flex items-center space-x-4">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Sort by:
          </label>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="popular">Most Popular</option>
            <option value="rating">Highest Rated</option>
            <option value="price-low">Price: Low to High</option>
            <option value="price-high">Price: High to Low</option>
          </select>
        </div>
      </div>

      {/* Dishes Grid */}
      {filteredDishes.length > 0 ? (
        <motion.div
          layout
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
        >
          {filteredDishes.map((dish, index) => (
            <motion.div
              key={dish.id}
              layout
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ delay: index * 0.1 }}
            >
              <DishCard dish={dish} />
            </motion.div>
          ))}
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-16"
        >
          <div className="text-6xl mb-4">🔍</div>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            No dishes found
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Try adjusting your search or filters
          </p>
        </motion.div>
      )}
    </div>
  )
}

export default FoodGrid
